import { graphql } from '~/gql'

export const GET_CATEGORIES_LIST = graphql(`
  query GetCategoriesList(
    $keyword: String
  ) {
    getCategories(
      keyword: $keyword
    ) {
      id
      name
      is_leaf
      is_classified
      children {
        id
        name
        is_leaf
        is_classified
        children {
          id
          name
          is_leaf
          is_classified
        }
      }
    }
  }
`)

export const UPDATE_CATEGORY = graphql(`
  mutation UpdateCategory(
    $id: ID!
    $is_classified: Boolean
    $name: String
    $parent_id: ID
  ) {
    updateCategory(
      id: $id
      is_classified: $is_classified
      name: $name
      parent_id: $parent_id
    ) 
  } 
`)

export const ADD_CATEGORY = graphql(`
  mutation AddCategory(
    $name: String!
    $parent_id: ID
    $is_classified: Boolean
  ) {
    addCategory(
      name: $name
      parent_id: $parent_id
      is_classified: $is_classified
    )
  }
`)