import PageHeader from '~/components/common/page-header'
import CategoryList from './category-list'
import useGetCategories from './use-get-categories'
import AddCategoryDialog from './add-category-dialog'

export default function Categories() {
  const { data } = useGetCategories()

  return (
    <div className="flex grow flex-col gap-4">
    <div className="flex justify-between max-w-2xl w-full pr-2">
      <PageHeader title="Categories" />
      <AddCategoryDialog categories={data} />
    </div>
      <CategoryList categories={data} />
    </div>
  )
}
