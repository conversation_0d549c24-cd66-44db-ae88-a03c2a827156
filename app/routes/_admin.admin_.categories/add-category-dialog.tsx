import { ChevronsUpDownIcon, CheckIcon } from "lucide-react";
import { useMemo } from "react";
import PlusIcon from "~/components/icons/plus-icon";
import { Button } from "~/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "~/components/ui/command";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "~/components/ui/dialog";
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import type { GetCategoriesListQuery } from "~/gql/graphql";
import { useAppForm } from "~/hooks/form";
import useBoolean from "~/hooks/use-boolean";
import { cn } from "~/lib/utils";

interface Props {
  categories?: GetCategoriesListQuery
}

export default function AddCategoryDialog({ categories }: Props) {
  const {isOpen, toggle } = useBoolean()
  const form = useAppForm({
    defaultValues: {
      name: '',
      parent_id: '',
      is_classified: false
    }
  })

  const flatCategories = useMemo(() => flattenCategories(categories), [categories])
  
  console.log("flat", flatCategories)

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button size="icon">
        <PlusIcon/>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Add new category
          </DialogTitle>
          <DialogDescription>
            Enter category details
          </DialogDescription>
        </DialogHeader>
        <form className="flex flex-col gap-4">
          <form.AppField
            name="name"
            children={field => <field.InputField label="Name" />}
          />
          <form.AppField
            name="parent_id"
            children={field => (
              <div className="space-y-2">
                <Popover open={isOpen} onOpenChange={toggle}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={isOpen}
                      className="w-full justify-between"
                    >
                      {field.state.value
                        ? flatCategories.find((cat) => cat.id === field.state.value)?.displayName
                        : "Select parent category..."}
                      <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandInput placeholder="Search categories..." />
                      <CommandList>
                        <CommandEmpty>No category found.</CommandEmpty>
                        <CommandGroup>
                          <CommandItem
                            value=""
                            onSelect={() => {
                              field.handleChange("")
                              toggle(false)
                            }}
                          >
                            <CheckIcon
                              className={cn(
                                "mr-2 h-4 w-4",
                                field.state.value === "" ? "opacity-100" : "opacity-0"
                              )}
                            />
                            No parent (root category)
                          </CommandItem>
                          {flatCategories.map((category) => (
                            <CommandItem
                              key={category.id}
                              value={category.id}
                              onSelect={(currentValue) => {
                                field.handleChange(currentValue === field.state.value ? "" : currentValue)
                                toggle(false)
                              }}
                            >
                              <CheckIcon
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  field.state.value === category.id ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {category.displayName}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            )}
          />

          <form.AppField
            name="is_classified"
            children={field => <field.CheckboxField label="Is Classified" />}
          />

        </form>
      </DialogContent>
    </Dialog>
  )
}
