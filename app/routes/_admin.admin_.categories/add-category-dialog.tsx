import type { GetCategoriesListQuery } from '~/gql/graphql'
import { CheckIcon, ChevronsUpDownIcon } from 'lucide-react'
import { useMemo } from 'react'
import PlusIcon from '~/components/icons/plus-icon'
import { Button } from '~/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '~/components/ui/command'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '~/components/ui/dialog'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import { cn } from '~/lib/utils'
import useAddCategory from './use-add-category'

interface Props {
  categories?: GetCategoriesListQuery
}

interface FlatCategory {
  id: string
  name: string
  displayName: string
  is_classified: boolean
}

function flattenCategories(categories?: GetCategoriesListQuery): FlatCategory[] {
  if (!categories?.getCategories)
    return []

  return categories.getCategories.flatMap((category) => {
    // Create parent category entry
    const parentCategory: FlatCategory = {
      id: category.id,
      name: category.name,
      displayName: category.name,
      is_classified: category.is_classified,
    }

    // Create child category entries (exclude grandchildren)
    const childCategories: FlatCategory[] = category.children?.map(child => ({
      id: child.id,
      name: child.name,
      displayName: `${category.name} | ${child.name}`,
      is_classified: child.is_classified,
    })) || []

    // Return parent + children (no grandchildren)
    return [parentCategory, ...childCategories]
  })
}

export default function AddCategoryDialog({ categories }: Props) {
  const { isOpen, toggle } = useBoolean()

  const { isOpen: dialogOpen, toggle: toggleDialog } = useBoolean()
  const { addCategory } = useAddCategory()

  const form = useAppForm({
    defaultValues: {
      name: '',
      parent_id: '',
      is_classified: false,
    },
    onSubmit: async ({ value }) => {
      addCategory.mutateAsync({
        parent_id: value.parent_id || undefined,
        name: value.name,
        is_classified: value.is_classified,
      }, {
        onSuccess() {
          toggle(false)
          form.reset()
        },
      })
    },
  })

  const flatCategories = useMemo(() => flattenCategories(categories), [categories])

  console.log('flatCate', flatCategories)

  return (
    <Dialog open={dialogOpen} onOpenChange={toggleDialog}>
      <DialogTrigger asChild>
        <Button size="icon">
          <PlusIcon />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Add new category
          </DialogTitle>
          <DialogDescription>
            Enter category details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-4"
        >
          <form.AppField
            name="name"
            children={field => <field.InputField label="Name" />}
          />
          <form.AppField
            name="parent_id"
            children={field => (
              <Popover open={isOpen} onOpenChange={toggle}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={isOpen}
                    className="w-full justify-between"
                  >
                    {field.state.value
                      ? flatCategories.find(cat => cat.id === field.state.value)?.displayName ?? '...'
                      : 'Select parent category...'}
                    <ChevronsUpDownIcon className={`
                      ml-2 h-4 w-4 shrink-0 opacity-50
                    `}
                    />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[450px] p-0">
                  <Command className="w-full">
                    <CommandInput placeholder="Search categories..." />
                    <CommandList>
                      <CommandEmpty>No category found.</CommandEmpty>
                      <CommandGroup>
                        {flatCategories.map(category => (
                          <CommandItem
                            key={category.id}
                            value={category.id}
                            onSelect={(currentValue) => {
                              field.handleChange(currentValue === field.state.value ? '' : currentValue)
                              toggle(false)
                            }}
                          >
                            <CheckIcon
                              className={cn(
                                'mr-2 h-4 w-4',
                                field.state.value === category.id
                                  ? `opacity-100`
                                  : `opacity-0`,
                              )}
                            />
                            {category.displayName}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            )}
          />
          <form.AppField
            name="is_classified"
            children={field => <field.CheckboxField label="Is Classified" />}
          />
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Add Category" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
