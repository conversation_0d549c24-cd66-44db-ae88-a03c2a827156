/* eslint-disable */
import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Date: { input: any; output: any; }
  /** A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`. */
  DateTime: { input: any; output: any; }
  Upload: { input: any; output: any; }
};

export type AppPaginator = {
  __typename?: 'AppPaginator';
  current_page: Scalars['Int']['output'];
  has_more_pages: Scalars['Boolean']['output'];
  last_page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type BaptismaRecord = {
  __typename?: 'BaptismaRecord';
  baptisma_chan_ni?: Maybe<Scalars['Date']['output']>;
  chantirtu?: Maybe<Scalars['String']['output']>;
  document: Document;
  hming: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  khua?: Maybe<Scalars['String']['output']>;
  nu_hming?: Maybe<Scalars['String']['output']>;
  pa_hming: Scalars['String']['output'];
  pian_ni?: Maybe<Scalars['Date']['output']>;
  registration_no: Scalars['String']['output'];
};

export type BaptismaRecordFilterInput = {
  baptisma_chan_ni?: InputMaybe<Scalars['Date']['input']>;
  chantirtu?: InputMaybe<Scalars['String']['input']>;
  hming?: InputMaybe<Scalars['String']['input']>;
  khua?: InputMaybe<Scalars['String']['input']>;
  nu_hming?: InputMaybe<Scalars['String']['input']>;
  pa_hming?: InputMaybe<Scalars['String']['input']>;
  pian_ni?: InputMaybe<Scalars['Date']['input']>;
  registration_no?: InputMaybe<Scalars['String']['input']>;
};

export type BaptismaRecordInput = {
  baptisma_chan_ni?: InputMaybe<Scalars['Date']['input']>;
  chantirtu?: InputMaybe<Scalars['String']['input']>;
  hming: Scalars['String']['input'];
  khua?: InputMaybe<Scalars['String']['input']>;
  nu_hming?: InputMaybe<Scalars['String']['input']>;
  pa_hming: Scalars['String']['input'];
  pian_ni?: InputMaybe<Scalars['Date']['input']>;
  registration_no: Scalars['String']['input'];
};

export type BaptismaRecordUpdateInput = {
  baptisma_chan_ni?: InputMaybe<Scalars['Date']['input']>;
  chantirtu?: InputMaybe<Scalars['String']['input']>;
  hming?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  khua?: InputMaybe<Scalars['String']['input']>;
  nu_hming?: InputMaybe<Scalars['String']['input']>;
  pa_hming?: InputMaybe<Scalars['String']['input']>;
  pian_ni?: InputMaybe<Scalars['Date']['input']>;
  registration_no?: InputMaybe<Scalars['String']['input']>;
};

export type Category = {
  __typename?: 'Category';
  children?: Maybe<Array<Category>>;
  id: Scalars['ID']['output'];
  is_classified: Scalars['Boolean']['output'];
  is_leaf: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  parent?: Maybe<Category>;
  parent_id: Scalars['Int']['output'];
  subCategory?: Maybe<Array<Category>>;
};

export type CategoryPaginator = {
  __typename?: 'CategoryPaginator';
  data?: Maybe<Array<Category>>;
  paginator_info: AppPaginator;
};

export type DocFile = {
  __typename?: 'DocFile';
  doc_id: Scalars['Int']['output'];
  document: Document;
  file_size: Scalars['Int']['output'];
  file_type?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  path: Scalars['String']['output'];
};

export type Document = {
  __typename?: 'Document';
  added_date: Scalars['Date']['output'];
  body?: Maybe<Scalars['String']['output']>;
  category?: Maybe<Category>;
  category_id?: Maybe<Scalars['ID']['output']>;
  extra_record?: Maybe<ExtraRecordMorph>;
  files?: Maybe<Array<DocFile>>;
  id: Scalars['ID']['output'];
  is_classified: Scalars['Boolean']['output'];
  tags?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type DocumentInput = {
  added_date?: InputMaybe<Scalars['Date']['input']>;
  body?: InputMaybe<Scalars['String']['input']>;
  category_id: Scalars['ID']['input'];
  files?: InputMaybe<Array<Scalars['Upload']['input']>>;
  is_classified: Scalars['Boolean']['input'];
  tags?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type DocumentUpdateInput = {
  added_date?: InputMaybe<Scalars['Date']['input']>;
  body?: InputMaybe<Scalars['String']['input']>;
  category_id?: InputMaybe<Scalars['ID']['input']>;
  files?: InputMaybe<Array<Scalars['Upload']['input']>>;
  id: Scalars['ID']['input'];
  is_classified?: InputMaybe<Scalars['Boolean']['input']>;
  tags?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type ExtraRecordMorph = BaptismaRecord | InneihRecord;

export type GetDocumentPaginator = {
  __typename?: 'GetDocumentPaginator';
  data?: Maybe<Array<Document>>;
  paginator_info: AppPaginator;
};

export type InneihRecord = {
  __typename?: 'InneihRecord';
  document: Document;
  hmeichhe_hming: Scalars['String']['output'];
  hmeichhe_pa_hming?: Maybe<Scalars['String']['output']>;
  hmun?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  inneih_ni?: Maybe<Scalars['Date']['output']>;
  inneihtirtu?: Maybe<Scalars['String']['output']>;
  mipa_hming: Scalars['String']['output'];
  mipa_pa_hming?: Maybe<Scalars['String']['output']>;
  registration_no?: Maybe<Scalars['String']['output']>;
};

export type InneihRecordFilterInput = {
  hmeichhe_hming?: InputMaybe<Scalars['String']['input']>;
  hmeichhe_pa_hming?: InputMaybe<Scalars['String']['input']>;
  hmun?: InputMaybe<Scalars['String']['input']>;
  inneih_ni?: InputMaybe<Scalars['Date']['input']>;
  inneihtirtu?: InputMaybe<Scalars['String']['input']>;
  mipa_hming?: InputMaybe<Scalars['String']['input']>;
  mipa_pa_hming?: InputMaybe<Scalars['String']['input']>;
  registration_no?: InputMaybe<Scalars['String']['input']>;
};

export type InneihRecordInput = {
  hmeichhe_hming: Scalars['String']['input'];
  hmeichhe_pa_hming?: InputMaybe<Scalars['String']['input']>;
  hmun?: InputMaybe<Scalars['String']['input']>;
  inneih_ni?: InputMaybe<Scalars['Date']['input']>;
  inneihtirtu?: InputMaybe<Scalars['String']['input']>;
  mipa_hming: Scalars['String']['input'];
  mipa_pa_hming?: InputMaybe<Scalars['String']['input']>;
  registration_no?: InputMaybe<Scalars['String']['input']>;
};

export type InneihRecordUpdateInput = {
  hmeichhe_hming?: InputMaybe<Scalars['String']['input']>;
  hmeichhe_pa_hming?: InputMaybe<Scalars['String']['input']>;
  hmun?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  inneih_ni?: InputMaybe<Scalars['Date']['input']>;
  inneihtirtu?: InputMaybe<Scalars['String']['input']>;
  mipa_hming?: InputMaybe<Scalars['String']['input']>;
  mipa_pa_hming?: InputMaybe<Scalars['String']['input']>;
  registration_no?: InputMaybe<Scalars['String']['input']>;
};

export type LoginResponse = {
  __typename?: 'LoginResponse';
  exp_date: Scalars['DateTime']['output'];
  token: Scalars['String']['output'];
  user: User;
};

export type Mutation = {
  __typename?: 'Mutation';
  addCategory: Scalars['Boolean']['output'];
  addRecord: Scalars['Boolean']['output'];
  addSpotlight: Scalars['Boolean']['output'];
  deleteCategory: Category;
  deleteSpotlight: Spotlight;
  login: LoginResponse;
  logout: Scalars['Boolean']['output'];
  updateCategory: Scalars['Boolean']['output'];
  updateRecord: Scalars['Boolean']['output'];
  updateSpotlight: Scalars['Boolean']['output'];
};


export type MutationAddCategoryArgs = {
  is_classified?: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['String']['input'];
  parent_id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationAddRecordArgs = {
  baptisma_record?: InputMaybe<BaptismaRecordInput>;
  document: DocumentInput;
  inneih_record?: InputMaybe<InneihRecordInput>;
};


export type MutationAddSpotlightArgs = {
  body: Scalars['String']['input'];
  image?: InputMaybe<Scalars['Upload']['input']>;
  title: Scalars['String']['input'];
};


export type MutationDeleteCategoryArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteSpotlightArgs = {
  id: Scalars['ID']['input'];
};


export type MutationLoginArgs = {
  name: Scalars['String']['input'];
  password: Scalars['String']['input'];
};


export type MutationUpdateCategoryArgs = {
  id: Scalars['ID']['input'];
  is_classified?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  parent_id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationUpdateRecordArgs = {
  baptisma_record?: InputMaybe<BaptismaRecordUpdateInput>;
  document: DocumentUpdateInput;
  inneih_record?: InputMaybe<InneihRecordUpdateInput>;
};


export type MutationUpdateSpotlightArgs = {
  body?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  image?: InputMaybe<Scalars['Upload']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

/** Allows ordering a list of records. */
export type OrderByClause = {
  /** The column that is used for ordering. */
  column: Scalars['String']['input'];
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Aggregate functions when ordering by a relation without specifying a column. */
export enum OrderByRelationAggregateFunction {
  /** Amount of items. */
  Count = 'COUNT'
}

/** Aggregate functions when ordering by a relation that may specify a column. */
export enum OrderByRelationWithColumnAggregateFunction {
  /** Average. */
  Avg = 'AVG',
  /** Amount of items. */
  Count = 'COUNT',
  /** Maximum. */
  Max = 'MAX',
  /** Minimum. */
  Min = 'MIN',
  /** Sum. */
  Sum = 'SUM'
}

export type OtherRecordInput = {
  added_date?: InputMaybe<Scalars['Date']['input']>;
  body?: InputMaybe<Scalars['String']['input']>;
  tag?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

/** Information about pagination using a fully featured paginator. */
export type PaginatorInfo = {
  __typename?: 'PaginatorInfo';
  /** Number of items in the current page. */
  count: Scalars['Int']['output'];
  /** Index of the current page. */
  currentPage: Scalars['Int']['output'];
  /** Index of the first item in the current page. */
  firstItem?: Maybe<Scalars['Int']['output']>;
  /** Are there more pages after this one? */
  hasMorePages: Scalars['Boolean']['output'];
  /** Index of the last item in the current page. */
  lastItem?: Maybe<Scalars['Int']['output']>;
  /** Index of the last available page. */
  lastPage: Scalars['Int']['output'];
  /** Number of items per page. */
  perPage: Scalars['Int']['output'];
  /** Number of total available items. */
  total: Scalars['Int']['output'];
};

/** Indicates what fields are available at the top level of a query operation. */
export type Query = {
  __typename?: 'Query';
  getCategories?: Maybe<Array<Category>>;
  getCategoryById: Category;
  getDocumentById: Document;
  getDocuments: GetDocumentPaginator;
  getSpotlightById: Spotlight;
  getSpotlights: SpotlightPaginator;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCategoriesArgs = {
  keyword?: InputMaybe<Scalars['String']['input']>;
  only_leaf?: InputMaybe<Scalars['Boolean']['input']>;
  unnested?: InputMaybe<Scalars['Boolean']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCategoryByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetDocumentByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetDocumentsArgs = {
  added_date?: InputMaybe<Scalars['Date']['input']>;
  baptisma_filter?: InputMaybe<BaptismaRecordFilterInput>;
  first: Scalars['Int']['input'];
  inneih_filter?: InputMaybe<InneihRecordFilterInput>;
  others_filter?: InputMaybe<OtherRecordInput>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetSpotlightByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetSpotlightsArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

/** Directions for ordering a list of records. */
export enum SortOrder {
  /** Sort records in ascending order. */
  Asc = 'ASC',
  /** Sort records in descending order. */
  Desc = 'DESC'
}

export type Spotlight = {
  __typename?: 'Spotlight';
  body: Scalars['String']['output'];
  file_path?: Maybe<Scalars['String']['output']>;
  file_size: Scalars['Int']['output'];
  file_type?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
};

/** A paginated list of Spotlight items. */
export type SpotlightPaginator = {
  __typename?: 'SpotlightPaginator';
  /** A list of Spotlight items. */
  data: Array<Spotlight>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

/** Specify if you want to include or exclude trashed results from a query. */
export enum Trashed {
  /** Only return trashed results. */
  Only = 'ONLY',
  /** Return both trashed and non-trashed results. */
  With = 'WITH',
  /** Only return non-trashed results. */
  Without = 'WITHOUT'
}

/** Account of a person who uses this application. */
export type User = {
  __typename?: 'User';
  /** When the account was created. */
  created_at: Scalars['DateTime']['output'];
  /** Unique primary key. */
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  /** When the account was last updated. */
  updated_at: Scalars['DateTime']['output'];
};

export type LogoutMutationVariables = Exact<{ [key: string]: never; }>;


export type LogoutMutation = { __typename?: 'Mutation', logout: boolean };

export type AddRecordMutationVariables = Exact<{
  inneih_record?: InputMaybe<InneihRecordInput>;
  baptisma_record?: InputMaybe<BaptismaRecordInput>;
  document: DocumentInput;
}>;


export type AddRecordMutation = { __typename?: 'Mutation', addRecord: boolean };

export type GetCategoriesQueryVariables = Exact<{
  keyword?: InputMaybe<Scalars['String']['input']>;
  only_leaf?: InputMaybe<Scalars['Boolean']['input']>;
  unnested?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type GetCategoriesQuery = { __typename?: 'Query', getCategories?: Array<{ __typename?: 'Category', id: string, name: string, parent?: { __typename?: 'Category', id: string, name: string, parent?: { __typename?: 'Category', id: string, name: string } | null } | null }> | null };

export type GetDocumentsQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  added_date?: InputMaybe<Scalars['Date']['input']>;
  baptisma_filter?: InputMaybe<BaptismaRecordFilterInput>;
  inneih_filter?: InputMaybe<InneihRecordFilterInput>;
  others_filter?: InputMaybe<OtherRecordInput>;
}>;


export type GetDocumentsQuery = { __typename?: 'Query', getDocuments: { __typename?: 'GetDocumentPaginator', data?: Array<{ __typename?: 'Document', id: string, title?: string | null, body?: string | null, tags?: string | null, added_date: any, category_id?: string | null, is_classified: boolean, extra_record?: { __typename: 'BaptismaRecord', id: string, hming: string, pa_hming: string, nu_hming?: string | null, pian_ni?: any | null, baptisma_chan_ni?: any | null, khua?: string | null, chantirtu?: string | null, baptisma_registration_no: string } | { __typename: 'InneihRecord', id: string, mipa_hming: string, mipa_pa_hming?: string | null, hmeichhe_hming: string, hmeichhe_pa_hming?: string | null, inneih_ni?: any | null, hmun?: string | null, inneihtirtu?: string | null, inneih_registration_no?: string | null } | null, files?: Array<{ __typename?: 'DocFile', id: string, doc_id: number, path: string }> | null }> | null } };

export type GetCategoriesListQueryVariables = Exact<{
  keyword?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetCategoriesListQuery = { __typename?: 'Query', getCategories?: Array<{ __typename?: 'Category', id: string, name: string, is_leaf: boolean, is_classified: boolean, children?: Array<{ __typename?: 'Category', id: string, name: string, is_leaf: boolean, is_classified: boolean, children?: Array<{ __typename?: 'Category', id: string, name: string, is_leaf: boolean, is_classified: boolean }> | null }> | null }> | null };

export type UpdateCategoryMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  is_classified?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  parent_id?: InputMaybe<Scalars['ID']['input']>;
}>;


export type UpdateCategoryMutation = { __typename?: 'Mutation', updateCategory: boolean };

export type LoginMutationVariables = Exact<{
  name: Scalars['String']['input'];
  password: Scalars['String']['input'];
}>;


export type LoginMutation = { __typename?: 'Mutation', login: { __typename?: 'LoginResponse', token: string, exp_date: any } };


export const LogoutDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"Logout"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"logout"}}]}}]} as unknown as DocumentNode<LogoutMutation, LogoutMutationVariables>;
export const AddRecordDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddRecord"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"inneih_record"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"InneihRecordInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"baptisma_record"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"BaptismaRecordInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"document"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DocumentInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"addRecord"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"inneih_record"},"value":{"kind":"Variable","name":{"kind":"Name","value":"inneih_record"}}},{"kind":"Argument","name":{"kind":"Name","value":"baptisma_record"},"value":{"kind":"Variable","name":{"kind":"Name","value":"baptisma_record"}}},{"kind":"Argument","name":{"kind":"Name","value":"document"},"value":{"kind":"Variable","name":{"kind":"Name","value":"document"}}}]}]}}]} as unknown as DocumentNode<AddRecordMutation, AddRecordMutationVariables>;
export const GetCategoriesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetCategories"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"only_leaf"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"unnested"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getCategories"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"keyword"},"value":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}}},{"kind":"Argument","name":{"kind":"Name","value":"only_leaf"},"value":{"kind":"Variable","name":{"kind":"Name","value":"only_leaf"}}},{"kind":"Argument","name":{"kind":"Name","value":"unnested"},"value":{"kind":"Variable","name":{"kind":"Name","value":"unnested"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"parent"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"parent"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetCategoriesQuery, GetCategoriesQueryVariables>;
export const GetDocumentsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetDocuments"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"added_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Date"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"baptisma_filter"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"BaptismaRecordFilterInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"inneih_filter"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"InneihRecordFilterInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"others_filter"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"OtherRecordInput"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getDocuments"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"added_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"added_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"baptisma_filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"baptisma_filter"}}},{"kind":"Argument","name":{"kind":"Name","value":"inneih_filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"inneih_filter"}}},{"kind":"Argument","name":{"kind":"Name","value":"others_filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"others_filter"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"body"}},{"kind":"Field","name":{"kind":"Name","value":"tags"}},{"kind":"Field","name":{"kind":"Name","value":"added_date"}},{"kind":"Field","name":{"kind":"Name","value":"category_id"}},{"kind":"Field","name":{"kind":"Name","value":"is_classified"}},{"kind":"Field","name":{"kind":"Name","value":"extra_record"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"InneihRecord"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","alias":{"kind":"Name","value":"inneih_registration_no"},"name":{"kind":"Name","value":"registration_no"}},{"kind":"Field","name":{"kind":"Name","value":"mipa_hming"}},{"kind":"Field","name":{"kind":"Name","value":"mipa_pa_hming"}},{"kind":"Field","name":{"kind":"Name","value":"hmeichhe_hming"}},{"kind":"Field","name":{"kind":"Name","value":"hmeichhe_pa_hming"}},{"kind":"Field","name":{"kind":"Name","value":"inneih_ni"}},{"kind":"Field","name":{"kind":"Name","value":"hmun"}},{"kind":"Field","name":{"kind":"Name","value":"inneihtirtu"}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"BaptismaRecord"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","alias":{"kind":"Name","value":"baptisma_registration_no"},"name":{"kind":"Name","value":"registration_no"}},{"kind":"Field","name":{"kind":"Name","value":"hming"}},{"kind":"Field","name":{"kind":"Name","value":"pa_hming"}},{"kind":"Field","name":{"kind":"Name","value":"nu_hming"}},{"kind":"Field","name":{"kind":"Name","value":"pian_ni"}},{"kind":"Field","name":{"kind":"Name","value":"baptisma_chan_ni"}},{"kind":"Field","name":{"kind":"Name","value":"khua"}},{"kind":"Field","name":{"kind":"Name","value":"chantirtu"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"files"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"doc_id"}},{"kind":"Field","name":{"kind":"Name","value":"path"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetDocumentsQuery, GetDocumentsQueryVariables>;
export const GetCategoriesListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetCategoriesList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getCategories"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"keyword"},"value":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"is_leaf"}},{"kind":"Field","name":{"kind":"Name","value":"is_classified"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"is_leaf"}},{"kind":"Field","name":{"kind":"Name","value":"is_classified"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"is_leaf"}},{"kind":"Field","name":{"kind":"Name","value":"is_classified"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetCategoriesListQuery, GetCategoriesListQueryVariables>;
export const UpdateCategoryDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateCategory"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"is_classified"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"parent_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateCategory"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"is_classified"},"value":{"kind":"Variable","name":{"kind":"Name","value":"is_classified"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"parent_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"parent_id"}}}]}]}}]} as unknown as DocumentNode<UpdateCategoryMutation, UpdateCategoryMutationVariables>;
export const LoginDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"Login"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"password"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"login"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"password"},"value":{"kind":"Variable","name":{"kind":"Name","value":"password"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"token"}},{"kind":"Field","name":{"kind":"Name","value":"exp_date"}}]}}]}}]} as unknown as DocumentNode<LoginMutation, LoginMutationVariables>;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Date: { input: any; output: any; }
  /** A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`. */
  DateTime: { input: any; output: any; }
  Upload: { input: any; output: any; }
};

export type AppPaginator = {
  __typename?: 'AppPaginator';
  current_page: Scalars['Int']['output'];
  has_more_pages: Scalars['Boolean']['output'];
  last_page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type BaptismaRecord = {
  __typename?: 'BaptismaRecord';
  baptisma_chan_ni?: Maybe<Scalars['Date']['output']>;
  chantirtu?: Maybe<Scalars['String']['output']>;
  document: Document;
  hming: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  khua?: Maybe<Scalars['String']['output']>;
  nu_hming?: Maybe<Scalars['String']['output']>;
  pa_hming: Scalars['String']['output'];
  pian_ni?: Maybe<Scalars['Date']['output']>;
  registration_no: Scalars['String']['output'];
};

export type BaptismaRecordFilterInput = {
  baptisma_chan_ni?: InputMaybe<Scalars['Date']['input']>;
  chantirtu?: InputMaybe<Scalars['String']['input']>;
  hming?: InputMaybe<Scalars['String']['input']>;
  khua?: InputMaybe<Scalars['String']['input']>;
  nu_hming?: InputMaybe<Scalars['String']['input']>;
  pa_hming?: InputMaybe<Scalars['String']['input']>;
  pian_ni?: InputMaybe<Scalars['Date']['input']>;
  registration_no?: InputMaybe<Scalars['String']['input']>;
};

export type BaptismaRecordInput = {
  baptisma_chan_ni?: InputMaybe<Scalars['Date']['input']>;
  chantirtu?: InputMaybe<Scalars['String']['input']>;
  hming: Scalars['String']['input'];
  khua?: InputMaybe<Scalars['String']['input']>;
  nu_hming?: InputMaybe<Scalars['String']['input']>;
  pa_hming: Scalars['String']['input'];
  pian_ni?: InputMaybe<Scalars['Date']['input']>;
  registration_no: Scalars['String']['input'];
};

export type BaptismaRecordUpdateInput = {
  baptisma_chan_ni?: InputMaybe<Scalars['Date']['input']>;
  chantirtu?: InputMaybe<Scalars['String']['input']>;
  hming?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  khua?: InputMaybe<Scalars['String']['input']>;
  nu_hming?: InputMaybe<Scalars['String']['input']>;
  pa_hming?: InputMaybe<Scalars['String']['input']>;
  pian_ni?: InputMaybe<Scalars['Date']['input']>;
  registration_no?: InputMaybe<Scalars['String']['input']>;
};

export type Category = {
  __typename?: 'Category';
  children?: Maybe<Array<Category>>;
  id: Scalars['ID']['output'];
  is_classified: Scalars['Boolean']['output'];
  is_leaf: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  parent?: Maybe<Category>;
  parent_id: Scalars['Int']['output'];
  subCategory?: Maybe<Array<Category>>;
};

export type CategoryPaginator = {
  __typename?: 'CategoryPaginator';
  data?: Maybe<Array<Category>>;
  paginator_info: AppPaginator;
};

export type DocFile = {
  __typename?: 'DocFile';
  doc_id: Scalars['Int']['output'];
  document: Document;
  file_size: Scalars['Int']['output'];
  file_type?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  path: Scalars['String']['output'];
};

export type Document = {
  __typename?: 'Document';
  added_date: Scalars['Date']['output'];
  body?: Maybe<Scalars['String']['output']>;
  category?: Maybe<Category>;
  category_id?: Maybe<Scalars['ID']['output']>;
  extra_record?: Maybe<ExtraRecordMorph>;
  files?: Maybe<Array<DocFile>>;
  id: Scalars['ID']['output'];
  is_classified: Scalars['Boolean']['output'];
  tags?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type DocumentInput = {
  added_date?: InputMaybe<Scalars['Date']['input']>;
  body?: InputMaybe<Scalars['String']['input']>;
  category_id: Scalars['ID']['input'];
  files?: InputMaybe<Array<Scalars['Upload']['input']>>;
  is_classified: Scalars['Boolean']['input'];
  tags?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type DocumentUpdateInput = {
  added_date?: InputMaybe<Scalars['Date']['input']>;
  body?: InputMaybe<Scalars['String']['input']>;
  category_id?: InputMaybe<Scalars['ID']['input']>;
  files?: InputMaybe<Array<Scalars['Upload']['input']>>;
  id: Scalars['ID']['input'];
  is_classified?: InputMaybe<Scalars['Boolean']['input']>;
  tags?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type ExtraRecordMorph = BaptismaRecord | InneihRecord;

export type GetDocumentPaginator = {
  __typename?: 'GetDocumentPaginator';
  data?: Maybe<Array<Document>>;
  paginator_info: AppPaginator;
};

export type InneihRecord = {
  __typename?: 'InneihRecord';
  document: Document;
  hmeichhe_hming: Scalars['String']['output'];
  hmeichhe_pa_hming?: Maybe<Scalars['String']['output']>;
  hmun?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  inneih_ni?: Maybe<Scalars['Date']['output']>;
  inneihtirtu?: Maybe<Scalars['String']['output']>;
  mipa_hming: Scalars['String']['output'];
  mipa_pa_hming?: Maybe<Scalars['String']['output']>;
  registration_no?: Maybe<Scalars['String']['output']>;
};

export type InneihRecordFilterInput = {
  hmeichhe_hming?: InputMaybe<Scalars['String']['input']>;
  hmeichhe_pa_hming?: InputMaybe<Scalars['String']['input']>;
  hmun?: InputMaybe<Scalars['String']['input']>;
  inneih_ni?: InputMaybe<Scalars['Date']['input']>;
  inneihtirtu?: InputMaybe<Scalars['String']['input']>;
  mipa_hming?: InputMaybe<Scalars['String']['input']>;
  mipa_pa_hming?: InputMaybe<Scalars['String']['input']>;
  registration_no?: InputMaybe<Scalars['String']['input']>;
};

export type InneihRecordInput = {
  hmeichhe_hming: Scalars['String']['input'];
  hmeichhe_pa_hming?: InputMaybe<Scalars['String']['input']>;
  hmun?: InputMaybe<Scalars['String']['input']>;
  inneih_ni?: InputMaybe<Scalars['Date']['input']>;
  inneihtirtu?: InputMaybe<Scalars['String']['input']>;
  mipa_hming: Scalars['String']['input'];
  mipa_pa_hming?: InputMaybe<Scalars['String']['input']>;
  registration_no?: InputMaybe<Scalars['String']['input']>;
};

export type InneihRecordUpdateInput = {
  hmeichhe_hming?: InputMaybe<Scalars['String']['input']>;
  hmeichhe_pa_hming?: InputMaybe<Scalars['String']['input']>;
  hmun?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  inneih_ni?: InputMaybe<Scalars['Date']['input']>;
  inneihtirtu?: InputMaybe<Scalars['String']['input']>;
  mipa_hming?: InputMaybe<Scalars['String']['input']>;
  mipa_pa_hming?: InputMaybe<Scalars['String']['input']>;
  registration_no?: InputMaybe<Scalars['String']['input']>;
};

export type LoginResponse = {
  __typename?: 'LoginResponse';
  exp_date: Scalars['DateTime']['output'];
  token: Scalars['String']['output'];
  user: User;
};

export type Mutation = {
  __typename?: 'Mutation';
  addCategory: Scalars['Boolean']['output'];
  addRecord: Scalars['Boolean']['output'];
  addSpotlight: Scalars['Boolean']['output'];
  deleteCategory: Category;
  deleteSpotlight: Spotlight;
  login: LoginResponse;
  logout: Scalars['Boolean']['output'];
  updateCategory: Scalars['Boolean']['output'];
  updateRecord: Scalars['Boolean']['output'];
  updateSpotlight: Scalars['Boolean']['output'];
};


export type MutationAddCategoryArgs = {
  is_classified?: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['String']['input'];
  parent_id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationAddRecordArgs = {
  baptisma_record?: InputMaybe<BaptismaRecordInput>;
  document: DocumentInput;
  inneih_record?: InputMaybe<InneihRecordInput>;
};


export type MutationAddSpotlightArgs = {
  body: Scalars['String']['input'];
  image?: InputMaybe<Scalars['Upload']['input']>;
  title: Scalars['String']['input'];
};


export type MutationDeleteCategoryArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteSpotlightArgs = {
  id: Scalars['ID']['input'];
};


export type MutationLoginArgs = {
  name: Scalars['String']['input'];
  password: Scalars['String']['input'];
};


export type MutationUpdateCategoryArgs = {
  id: Scalars['ID']['input'];
  is_classified?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  parent_id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationUpdateRecordArgs = {
  baptisma_record?: InputMaybe<BaptismaRecordUpdateInput>;
  document: DocumentUpdateInput;
  inneih_record?: InputMaybe<InneihRecordUpdateInput>;
};


export type MutationUpdateSpotlightArgs = {
  body?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  image?: InputMaybe<Scalars['Upload']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

/** Allows ordering a list of records. */
export type OrderByClause = {
  /** The column that is used for ordering. */
  column: Scalars['String']['input'];
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Aggregate functions when ordering by a relation without specifying a column. */
export enum OrderByRelationAggregateFunction {
  /** Amount of items. */
  Count = 'COUNT'
}

/** Aggregate functions when ordering by a relation that may specify a column. */
export enum OrderByRelationWithColumnAggregateFunction {
  /** Average. */
  Avg = 'AVG',
  /** Amount of items. */
  Count = 'COUNT',
  /** Maximum. */
  Max = 'MAX',
  /** Minimum. */
  Min = 'MIN',
  /** Sum. */
  Sum = 'SUM'
}

export type OtherRecordInput = {
  added_date?: InputMaybe<Scalars['Date']['input']>;
  body?: InputMaybe<Scalars['String']['input']>;
  tag?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

/** Information about pagination using a fully featured paginator. */
export type PaginatorInfo = {
  __typename?: 'PaginatorInfo';
  /** Number of items in the current page. */
  count: Scalars['Int']['output'];
  /** Index of the current page. */
  currentPage: Scalars['Int']['output'];
  /** Index of the first item in the current page. */
  firstItem?: Maybe<Scalars['Int']['output']>;
  /** Are there more pages after this one? */
  hasMorePages: Scalars['Boolean']['output'];
  /** Index of the last item in the current page. */
  lastItem?: Maybe<Scalars['Int']['output']>;
  /** Index of the last available page. */
  lastPage: Scalars['Int']['output'];
  /** Number of items per page. */
  perPage: Scalars['Int']['output'];
  /** Number of total available items. */
  total: Scalars['Int']['output'];
};

/** Indicates what fields are available at the top level of a query operation. */
export type Query = {
  __typename?: 'Query';
  getCategories?: Maybe<Array<Category>>;
  getCategoryById: Category;
  getDocumentById: Document;
  getDocuments: GetDocumentPaginator;
  getSpotlightById: Spotlight;
  getSpotlights: SpotlightPaginator;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCategoriesArgs = {
  keyword?: InputMaybe<Scalars['String']['input']>;
  only_leaf?: InputMaybe<Scalars['Boolean']['input']>;
  unnested?: InputMaybe<Scalars['Boolean']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCategoryByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetDocumentByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetDocumentsArgs = {
  added_date?: InputMaybe<Scalars['Date']['input']>;
  baptisma_filter?: InputMaybe<BaptismaRecordFilterInput>;
  first: Scalars['Int']['input'];
  inneih_filter?: InputMaybe<InneihRecordFilterInput>;
  others_filter?: InputMaybe<OtherRecordInput>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetSpotlightByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetSpotlightsArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};

/** Directions for ordering a list of records. */
export enum SortOrder {
  /** Sort records in ascending order. */
  Asc = 'ASC',
  /** Sort records in descending order. */
  Desc = 'DESC'
}

export type Spotlight = {
  __typename?: 'Spotlight';
  body: Scalars['String']['output'];
  file_path?: Maybe<Scalars['String']['output']>;
  file_size: Scalars['Int']['output'];
  file_type?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
};

/** A paginated list of Spotlight items. */
export type SpotlightPaginator = {
  __typename?: 'SpotlightPaginator';
  /** A list of Spotlight items. */
  data: Array<Spotlight>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

/** Specify if you want to include or exclude trashed results from a query. */
export enum Trashed {
  /** Only return trashed results. */
  Only = 'ONLY',
  /** Return both trashed and non-trashed results. */
  With = 'WITH',
  /** Only return non-trashed results. */
  Without = 'WITHOUT'
}

/** Account of a person who uses this application. */
export type User = {
  __typename?: 'User';
  /** When the account was created. */
  created_at: Scalars['DateTime']['output'];
  /** Unique primary key. */
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  /** When the account was last updated. */
  updated_at: Scalars['DateTime']['output'];
};

export type LogoutMutationVariables = Exact<{ [key: string]: never; }>;


export type LogoutMutation = { __typename?: 'Mutation', logout: boolean };

export type AddRecordMutationVariables = Exact<{
  inneih_record?: InputMaybe<InneihRecordInput>;
  baptisma_record?: InputMaybe<BaptismaRecordInput>;
  document: DocumentInput;
}>;


export type AddRecordMutation = { __typename?: 'Mutation', addRecord: boolean };

export type GetCategoriesQueryVariables = Exact<{
  keyword?: InputMaybe<Scalars['String']['input']>;
  only_leaf?: InputMaybe<Scalars['Boolean']['input']>;
  unnested?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type GetCategoriesQuery = { __typename?: 'Query', getCategories?: Array<{ __typename?: 'Category', id: string, name: string, parent?: { __typename?: 'Category', id: string, name: string, parent?: { __typename?: 'Category', id: string, name: string } | null } | null }> | null };

export type GetDocumentsQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  added_date?: InputMaybe<Scalars['Date']['input']>;
  baptisma_filter?: InputMaybe<BaptismaRecordFilterInput>;
  inneih_filter?: InputMaybe<InneihRecordFilterInput>;
  others_filter?: InputMaybe<OtherRecordInput>;
}>;


export type GetDocumentsQuery = { __typename?: 'Query', getDocuments: { __typename?: 'GetDocumentPaginator', data?: Array<{ __typename?: 'Document', id: string, title?: string | null, body?: string | null, tags?: string | null, added_date: any, category_id?: string | null, is_classified: boolean, extra_record?: { __typename: 'BaptismaRecord', id: string, hming: string, pa_hming: string, nu_hming?: string | null, pian_ni?: any | null, baptisma_chan_ni?: any | null, khua?: string | null, chantirtu?: string | null, baptisma_registration_no: string } | { __typename: 'InneihRecord', id: string, mipa_hming: string, mipa_pa_hming?: string | null, hmeichhe_hming: string, hmeichhe_pa_hming?: string | null, inneih_ni?: any | null, hmun?: string | null, inneihtirtu?: string | null, inneih_registration_no?: string | null } | null, files?: Array<{ __typename?: 'DocFile', id: string, doc_id: number, path: string }> | null }> | null } };

export type GetCategoriesListQueryVariables = Exact<{
  keyword?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetCategoriesListQuery = { __typename?: 'Query', getCategories?: Array<{ __typename?: 'Category', id: string, name: string, is_leaf: boolean, is_classified: boolean, children?: Array<{ __typename?: 'Category', id: string, name: string, is_leaf: boolean, is_classified: boolean, children?: Array<{ __typename?: 'Category', id: string, name: string, is_leaf: boolean, is_classified: boolean }> | null }> | null }> | null };

export type UpdateCategoryMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  is_classified?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  parent_id?: InputMaybe<Scalars['ID']['input']>;
}>;


export type UpdateCategoryMutation = { __typename?: 'Mutation', updateCategory: boolean };

export type LoginMutationVariables = Exact<{
  name: Scalars['String']['input'];
  password: Scalars['String']['input'];
}>;


export type LoginMutation = { __typename?: 'Mutation', login: { __typename?: 'LoginResponse', token: string, exp_date: any } };
