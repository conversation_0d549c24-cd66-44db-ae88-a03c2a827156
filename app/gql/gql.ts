/* eslint-disable */
import * as types from './graphql';
import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  mutation Logout {\n    logout \n  }\n": typeof types.LogoutDocument,
    "\n  mutation AddRecord(\n    $inneih_record: InneihRecordInput\n    $baptisma_record: BaptismaRecordInput\n    $document: DocumentInput!\n  ) {\n    addRecord(\n      inneih_record: $inneih_record\n      baptisma_record: $baptisma_record\n      document: $document\n    ) \n  }\n": typeof types.AddRecordDocument,
    "\n  query GetCategories(\n    $keyword: String\n    $only_leaf: Boolean\n    $unnested: Boolean\n  ) {\n    getCategories(\n      keyword: $keyword\n      only_leaf: $only_leaf\n      unnested: $unnested\n    ) {\n      id\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n  }\n": typeof types.GetCategoriesDocument,
    "\n  query GetDocuments(\n    $first: Int!\n    $page: Int\n    $added_date: Date\n    $baptisma_filter: BaptismaRecordFilterInput\n    $inneih_filter: InneihRecordFilterInput\n    $others_filter: OtherRecordInput\n  ) {\n    getDocuments(\n      first: $first\n      page: $page\n      added_date: $added_date\n      baptisma_filter: $baptisma_filter\n      inneih_filter: $inneih_filter\n      others_filter: $others_filter\n    ) {\n      data {\n        id\n        title\n        body\n        tags\n        added_date\n        category_id\n        is_classified\n        extra_record {\n          __typename\n          ... on InneihRecord {\n            id\n            inneih_registration_no: registration_no\n            mipa_hming\n            mipa_pa_hming\n            hmeichhe_hming\n            hmeichhe_pa_hming\n            inneih_ni\n            hmun\n            inneihtirtu\n          }\n          ... on BaptismaRecord {\n            id\n            baptisma_registration_no: registration_no\n            hming\n            pa_hming\n            nu_hming\n            pian_ni\n            baptisma_chan_ni\n            khua\n            chantirtu\n          }\n        }\n        files {\n          id\n          doc_id\n          path\n        }\n      }\n    }\n  }\n": typeof types.GetDocumentsDocument,
    "\n  query GetCategoriesList(\n    $keyword: String\n  ) {\n    getCategories(\n      keyword: $keyword\n    ) {\n      id\n      name\n      is_leaf\n      is_classified\n      children {\n        id\n        name\n        is_leaf\n        is_classified\n        children {\n          id\n          name\n          is_leaf\n          is_classified\n        }\n      }\n    }\n  }\n": typeof types.GetCategoriesListDocument,
    "\n  mutation UpdateCategory(\n    $id: ID!\n    $is_classified: Boolean\n    $name: String\n    $parent_id: ID\n  ) {\n    updateCategory(\n      id: $id\n      is_classified: $is_classified\n      name: $name\n      parent_id: $parent_id\n    ) \n  } \n": typeof types.UpdateCategoryDocument,
    "\n  mutation AddCategory(\n    $name: String!\n    $parent_id: ID\n    $is_classified: Boolean\n  ) {\n    addCategory(\n      name: $name\n      parent_id: $parent_id\n      is_classified: $is_classified\n    )\n  }\n": typeof types.AddCategoryDocument,
    "\n  mutation Login(\n    $name: String!\n    $password: String!\n  ) {\n    login(name: $name, password: $password) {\n      token\n      exp_date\n    }\n  }\n": typeof types.LoginDocument,
};
const documents: Documents = {
    "\n  mutation Logout {\n    logout \n  }\n": types.LogoutDocument,
    "\n  mutation AddRecord(\n    $inneih_record: InneihRecordInput\n    $baptisma_record: BaptismaRecordInput\n    $document: DocumentInput!\n  ) {\n    addRecord(\n      inneih_record: $inneih_record\n      baptisma_record: $baptisma_record\n      document: $document\n    ) \n  }\n": types.AddRecordDocument,
    "\n  query GetCategories(\n    $keyword: String\n    $only_leaf: Boolean\n    $unnested: Boolean\n  ) {\n    getCategories(\n      keyword: $keyword\n      only_leaf: $only_leaf\n      unnested: $unnested\n    ) {\n      id\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n  }\n": types.GetCategoriesDocument,
    "\n  query GetDocuments(\n    $first: Int!\n    $page: Int\n    $added_date: Date\n    $baptisma_filter: BaptismaRecordFilterInput\n    $inneih_filter: InneihRecordFilterInput\n    $others_filter: OtherRecordInput\n  ) {\n    getDocuments(\n      first: $first\n      page: $page\n      added_date: $added_date\n      baptisma_filter: $baptisma_filter\n      inneih_filter: $inneih_filter\n      others_filter: $others_filter\n    ) {\n      data {\n        id\n        title\n        body\n        tags\n        added_date\n        category_id\n        is_classified\n        extra_record {\n          __typename\n          ... on InneihRecord {\n            id\n            inneih_registration_no: registration_no\n            mipa_hming\n            mipa_pa_hming\n            hmeichhe_hming\n            hmeichhe_pa_hming\n            inneih_ni\n            hmun\n            inneihtirtu\n          }\n          ... on BaptismaRecord {\n            id\n            baptisma_registration_no: registration_no\n            hming\n            pa_hming\n            nu_hming\n            pian_ni\n            baptisma_chan_ni\n            khua\n            chantirtu\n          }\n        }\n        files {\n          id\n          doc_id\n          path\n        }\n      }\n    }\n  }\n": types.GetDocumentsDocument,
    "\n  query GetCategoriesList(\n    $keyword: String\n  ) {\n    getCategories(\n      keyword: $keyword\n    ) {\n      id\n      name\n      is_leaf\n      is_classified\n      children {\n        id\n        name\n        is_leaf\n        is_classified\n        children {\n          id\n          name\n          is_leaf\n          is_classified\n        }\n      }\n    }\n  }\n": types.GetCategoriesListDocument,
    "\n  mutation UpdateCategory(\n    $id: ID!\n    $is_classified: Boolean\n    $name: String\n    $parent_id: ID\n  ) {\n    updateCategory(\n      id: $id\n      is_classified: $is_classified\n      name: $name\n      parent_id: $parent_id\n    ) \n  } \n": types.UpdateCategoryDocument,
    "\n  mutation AddCategory(\n    $name: String!\n    $parent_id: ID\n    $is_classified: Boolean\n  ) {\n    addCategory(\n      name: $name\n      parent_id: $parent_id\n      is_classified: $is_classified\n    )\n  }\n": types.AddCategoryDocument,
    "\n  mutation Login(\n    $name: String!\n    $password: String!\n  ) {\n    login(name: $name, password: $password) {\n      token\n      exp_date\n    }\n  }\n": types.LoginDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Logout {\n    logout \n  }\n"): (typeof documents)["\n  mutation Logout {\n    logout \n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddRecord(\n    $inneih_record: InneihRecordInput\n    $baptisma_record: BaptismaRecordInput\n    $document: DocumentInput!\n  ) {\n    addRecord(\n      inneih_record: $inneih_record\n      baptisma_record: $baptisma_record\n      document: $document\n    ) \n  }\n"): (typeof documents)["\n  mutation AddRecord(\n    $inneih_record: InneihRecordInput\n    $baptisma_record: BaptismaRecordInput\n    $document: DocumentInput!\n  ) {\n    addRecord(\n      inneih_record: $inneih_record\n      baptisma_record: $baptisma_record\n      document: $document\n    ) \n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCategories(\n    $keyword: String\n    $only_leaf: Boolean\n    $unnested: Boolean\n  ) {\n    getCategories(\n      keyword: $keyword\n      only_leaf: $only_leaf\n      unnested: $unnested\n    ) {\n      id\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetCategories(\n    $keyword: String\n    $only_leaf: Boolean\n    $unnested: Boolean\n  ) {\n    getCategories(\n      keyword: $keyword\n      only_leaf: $only_leaf\n      unnested: $unnested\n    ) {\n      id\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetDocuments(\n    $first: Int!\n    $page: Int\n    $added_date: Date\n    $baptisma_filter: BaptismaRecordFilterInput\n    $inneih_filter: InneihRecordFilterInput\n    $others_filter: OtherRecordInput\n  ) {\n    getDocuments(\n      first: $first\n      page: $page\n      added_date: $added_date\n      baptisma_filter: $baptisma_filter\n      inneih_filter: $inneih_filter\n      others_filter: $others_filter\n    ) {\n      data {\n        id\n        title\n        body\n        tags\n        added_date\n        category_id\n        is_classified\n        extra_record {\n          __typename\n          ... on InneihRecord {\n            id\n            inneih_registration_no: registration_no\n            mipa_hming\n            mipa_pa_hming\n            hmeichhe_hming\n            hmeichhe_pa_hming\n            inneih_ni\n            hmun\n            inneihtirtu\n          }\n          ... on BaptismaRecord {\n            id\n            baptisma_registration_no: registration_no\n            hming\n            pa_hming\n            nu_hming\n            pian_ni\n            baptisma_chan_ni\n            khua\n            chantirtu\n          }\n        }\n        files {\n          id\n          doc_id\n          path\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetDocuments(\n    $first: Int!\n    $page: Int\n    $added_date: Date\n    $baptisma_filter: BaptismaRecordFilterInput\n    $inneih_filter: InneihRecordFilterInput\n    $others_filter: OtherRecordInput\n  ) {\n    getDocuments(\n      first: $first\n      page: $page\n      added_date: $added_date\n      baptisma_filter: $baptisma_filter\n      inneih_filter: $inneih_filter\n      others_filter: $others_filter\n    ) {\n      data {\n        id\n        title\n        body\n        tags\n        added_date\n        category_id\n        is_classified\n        extra_record {\n          __typename\n          ... on InneihRecord {\n            id\n            inneih_registration_no: registration_no\n            mipa_hming\n            mipa_pa_hming\n            hmeichhe_hming\n            hmeichhe_pa_hming\n            inneih_ni\n            hmun\n            inneihtirtu\n          }\n          ... on BaptismaRecord {\n            id\n            baptisma_registration_no: registration_no\n            hming\n            pa_hming\n            nu_hming\n            pian_ni\n            baptisma_chan_ni\n            khua\n            chantirtu\n          }\n        }\n        files {\n          id\n          doc_id\n          path\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCategoriesList(\n    $keyword: String\n  ) {\n    getCategories(\n      keyword: $keyword\n    ) {\n      id\n      name\n      is_leaf\n      is_classified\n      children {\n        id\n        name\n        is_leaf\n        is_classified\n        children {\n          id\n          name\n          is_leaf\n          is_classified\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetCategoriesList(\n    $keyword: String\n  ) {\n    getCategories(\n      keyword: $keyword\n    ) {\n      id\n      name\n      is_leaf\n      is_classified\n      children {\n        id\n        name\n        is_leaf\n        is_classified\n        children {\n          id\n          name\n          is_leaf\n          is_classified\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateCategory(\n    $id: ID!\n    $is_classified: Boolean\n    $name: String\n    $parent_id: ID\n  ) {\n    updateCategory(\n      id: $id\n      is_classified: $is_classified\n      name: $name\n      parent_id: $parent_id\n    ) \n  } \n"): (typeof documents)["\n  mutation UpdateCategory(\n    $id: ID!\n    $is_classified: Boolean\n    $name: String\n    $parent_id: ID\n  ) {\n    updateCategory(\n      id: $id\n      is_classified: $is_classified\n      name: $name\n      parent_id: $parent_id\n    ) \n  } \n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddCategory(\n    $name: String!\n    $parent_id: ID\n    $is_classified: Boolean\n  ) {\n    addCategory(\n      name: $name\n      parent_id: $parent_id\n      is_classified: $is_classified\n    )\n  }\n"): (typeof documents)["\n  mutation AddCategory(\n    $name: String!\n    $parent_id: ID\n    $is_classified: Boolean\n  ) {\n    addCategory(\n      name: $name\n      parent_id: $parent_id\n      is_classified: $is_classified\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Login(\n    $name: String!\n    $password: String!\n  ) {\n    login(name: $name, password: $password) {\n      token\n      exp_date\n    }\n  }\n"): (typeof documents)["\n  mutation Login(\n    $name: String!\n    $password: String!\n  ) {\n    login(name: $name, password: $password) {\n      token\n      exp_date\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;